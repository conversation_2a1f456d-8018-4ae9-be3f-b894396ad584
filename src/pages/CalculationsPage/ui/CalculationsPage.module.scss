.HeaderCustomWithButton {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 10px !important;
}

.TitleVault {
  margin-right: 20px;
}

.TitleStation {
  margin-right: 20px;
}

.pageContainer {
  height: 100%;
  width: 100%;
}

.contentContainer {
  display: flex;
  gap: 0.2rem;
  height: 100%;
  width: 100%;
}

.EmptyContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.HeaderNotRge {
  font-weight: bold;
}

.LeftContainer {
  width: 87.5%;
}

.main {
  flex-grow: 1;
  padding: 0.2rem;
  border-radius: 8px;
  background-color: var(--background-color-secondary);
  box-shadow: var(--shadow-page);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: auto;
  overflow-x: clip;
  scrollbar-gutter: stable; // Резервирование места под скроллбар для избежания сдвига контента
}

.title {
  grid-column: 1 / 13;
}

.LoaderUp {
  align-items: flex-end;
}

.LoaderDown {
  align-items: flex-start;
}

.Loader {
  margin-top: -40px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  background-color: var(--background-color-primary);
  z-index: 99999;
}

.plantsNotSelected {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color-primary);
}

.vaultSpreadsheet {
  &Container {
    height: 512px;
    width: 100%;
  }
  &Loader {
    height: 100%;
    width: 100%;
  }
}

.Up {
  display: flex;
  width: 100%;

  &.stationRgusFullWidth {
    gap: 20px;
    min-height: 501px;
  }

  &.stationFullWidth {
    gap: 20px;
    min-height: 482px;
  }

  div {
    font-size: 0.75rem;
  }

  td {
    & > div {
      min-height: 16px !important;
      max-height: 16px !important;
      height: 16px !important;
    }

    & > div > div > div {
      height: 16px !important;
    }
  }
}

.isNotVault {
  margin-top: 40px;
}

.Middle {
  height: 30px;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.Down {
  width: 100%;
  &.fullWidth {
    height: 38%;
  }
}

.DownContainer {
  display: flex;
  align-items: center;
  width: 100%;

  div {
    font-size: 0.75rem;
  }

  td {
    & > div {
      min-height: 16px !important;
      max-height: 16px !important;
      height: 16px !important;
    }

    & > div > div > div {
      height: 16px !important;
    }
  }
}

.HeaderForStationRow {
  display: flex;
  align-items: center;
  justify-content: center;
}

.HeaderForStationInput {
  width: 100px;
}

.ExportButton {
  position: relative;
  bottom: 2px;
  width: 20px !important;
  height: 20px !important;
  max-width: 20px !important;
  max-height: 20px !important;
  min-width: 20px !important;
  min-height: 20px !important;
  padding: 0 !important;
  color: #185C37 !important;
}

.ButtonContainer {
  display: flex;
  justify-content: center;
  align-items: center;

  &.dashedBorder {
    border: 1px dashed var(--blue-color);
    border-radius: 6px;
    height: 26px;
    background-color: var(--background-color-secondary);
  }
}

.RightGap {
  padding-right: 61px;
}

.ButtonModesContainer {
  @extend .ButtonContainer;

  padding: 0 26px;
  width: 118px;
  justify-content: space-between;
}

.disabledButton {
  color: var(--text-gray) !important;
}

.Plus {
  width: 14px !important;
  height: 14px !important;
  max-width: 14px !important;
  max-height: 14px !important;
  min-height: 14px !important;
  min-width: 14px !important;
  padding: 0 !important;
  color: var(--text-color);
}

.PlusContainer {
  display: flex;
  align-items: center;
}

.telemetry {
  grid-column: 10 / 13;
}

.telemetryGraph {
  flex-shrink: 0;
  width: 50%;
  grid-column: 7 / 13;
}

.header {
  height: var(--header-page);
  background-color: var(--background-color-secondary);
  border-radius: 8px;
  box-shadow: var(--shadow-page);
  padding: 0 16px;
}

.bold {
  font-weight: 700 !important;
}
