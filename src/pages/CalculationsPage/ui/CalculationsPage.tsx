import { StagesInitStatus } from 'entities/store/calculationPageStore.entities.ts'
import { observer } from 'mobx-react'
import { NotFoundStage } from 'pages/CalculationsPage/ui/NotFoundStage'
import { StationBody } from 'pages/CalculationsPage/ui/StationBody'
import { VaultBody } from 'pages/CalculationsPage/ui/VaultBody'
import { useEffect, useState } from 'react'
import { CheckEditComponent } from 'shared/ui/CheckEditComponent'
import { useStore } from 'stores/useStore.ts'

import { Aside } from './Aside'
import cls from './CalculationsPage.module.scss'

const CalculationsPage = observer(() => {
  const { calculationsPageStore } = useStore()
  const { avrchmStore, vaultStore, selectLeftMenu, isStagesExist, isSomePlantSelected } = calculationsPageStore
  const { isChangedAvrchmSpreadsheet } = avrchmStore
  const { isChangeFloodMode, isEditRows: vaultIsEditRows } = vaultStore

  const [editCellsUp, setEditCellsUp] = useState<string[]>([])
  const [isInputParams, setIsInputParams] = useState(false)

  const isEditRows =
    editCellsUp.length > 0 || isInputParams || isChangeFloodMode || isChangedAvrchmSpreadsheet || vaultIsEditRows

  useEffect(() => {
    return () => {
      calculationsPageStore.resetStore()
    }
  }, [])

  if (isStagesExist === StagesInitStatus.loadedWithoutData) {
    return <NotFoundStage />
  }

  return (
    <div className={cls.pageContainer}>
      <CheckEditComponent isEdit={isEditRows}>
        <div className={cls.contentContainer}>
          <Aside isEditRows={isEditRows} />
          {!isSomePlantSelected ? (
            <div className={cls.plantsNotSelected}>Выберите станцию</div>
          ) : (
            <>
              {selectLeftMenu === 0 ? (
                <VaultBody />
              ) : (
                <StationBody
                  isEditRows={isEditRows}
                  setEditCellsUp={setEditCellsUp}
                  editCellsUp={editCellsUp}
                  setIsInputParams={setIsInputParams}
                />
              )}
            </>
          )}
        </div>
      </CheckEditComponent>
    </div>
  )
})

export default CalculationsPage
