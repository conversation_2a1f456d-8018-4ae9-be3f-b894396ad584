import { IRguCalculation } from 'entities/api/calculationsManager.entities.ts'
import { PlantType } from 'entities/shared/common.entities.ts'
import Handsontable from 'handsontable'
import { CellValue } from 'handsontable/common'
import Highcharts, { SeriesColumnOptions, SeriesLineOptions, XAxisOptions } from 'highcharts'
import { observer } from 'mobx-react'
import { hours } from 'pages/CalculationsPage/ui/StationBody/lib'
import { Dispatch, FC, SetStateAction, useCallback, useEffect, useState } from 'react'
import { Chart } from 'shared/ui/Chart'
import { ChartProps } from 'shared/ui/Chart/Chart'
import { Icon } from 'shared/ui/Icon'
import { Toggle } from 'shared/ui/Toggle'
import { useStore } from 'stores/useStore.ts'

import cls from './PlannedValuesChart.module.scss'

const CHART_HEIGHT = 330

type SeriesOptionsType = SeriesColumnOptions | SeriesLineOptions

const getGesSeriesOptions = (data: CellValue[], plantRgus: IRguCalculation[]): SeriesOptionsType[] => [
  {
    name: 'Потребление',
    data: data.map((row) => Number(row[data[0].length - 1])),
    color: '#fff',
    borderColor: '#ffae00',
    type: 'column',
    yAxis: 0,
    groupPadding: 0,
    visible: true,
    borderWidth: 3,
    pointPadding: 0,
  },
  {
    name: 'РМ (мин)',
    data: data.map((row) =>
      Number(row[(plantRgus.length + 1) * 3 + (plantRgus.length + 1) * 2 + (plantRgus.length + 1) * 2]),
    ),
    color: '#661E9C',
    dashStyle: 'Dot',
    type: 'line',
    yAxis: 1,
    marker: {
      enabled: false,
    },
    visible: true,
  },
  {
    name: 'РМ (макс)',
    data: data.map((row) =>
      Number(row[(plantRgus.length + 1) * 3 + (plantRgus.length + 1) * 2 + (plantRgus.length + 1) * 2 + 1]),
    ),
    color: '#D92424',
    dashStyle: 'Dot',
    type: 'line',
    yAxis: 1,
    marker: {
      enabled: false,
    },
    visible: true,
  },
  {
    name: 'Модес (мин)',
    data: data.map((row) =>
      Number(
        row[
          (plantRgus.length + 1) * 3 +
            (plantRgus.length + 1) * 2 +
            (plantRgus.length + 1) * 2 +
            (plantRgus.length + 1) * 2
        ],
      ),
    ),
    color: '#661E9C',
    yAxis: 1,
    type: 'line',
    marker: {
      enabled: false,
    },
    dashStyle: 'Dash',
    visible: true,
  },
  {
    name: 'Модес (макс)',
    data: data.map((row) =>
      Number(
        row[
          (plantRgus.length + 1) * 3 +
            (plantRgus.length + 1) * 2 +
            (plantRgus.length + 1) * 2 +
            (plantRgus.length + 1) * 2 +
            1
        ],
      ),
    ),
    color: '#D92424',
    yAxis: 1,
    type: 'line',
    marker: {
      enabled: false,
    },
    dashStyle: 'Dash',
    visible: true,
  },
  {
    name: 'Итог (мин)',
    data: data.map((row) => Number(row[0])),
    color: '#661E9C',
    yAxis: 1,
    type: 'line',
    marker: {
      enabled: false,
    },
    visible: true,
  },
  {
    name: 'Итог (план)',
    data: data.map((row) => Number(row[1])),
    color: '#4768BC',
    yAxis: 1,
    type: 'line',
    marker: {
      enabled: false,
    },
    zIndex: 5,
    visible: true,
  },
  {
    name: 'Итог (макс)',
    data: data.map((row) => Number(row[2])),
    color: '#D92424',
    yAxis: 1,
    type: 'line',
    marker: {
      enabled: false,
    },
    visible: true,
  },
]

const getDefaultSeriesOptions = (data: CellValue[]): SeriesOptionsType[] => [
  {
    name: 'Потребление',
    data: data.map((row) => Number(row[4])),
    color: '#fff',
    borderColor: '#ffae00',
    type: 'column',
    yAxis: 0,
    groupPadding: 0,
    visible: true,
    borderWidth: 3,
    pointPadding: 0,
  },
  {
    name: 'Итог (мин)',
    data: data.map((row) => Number(row[0])),
    color: '#661E9C',
    yAxis: 1,
    type: 'line',
    marker: {
      enabled: false,
    },
    visible: true,
  },
  {
    name: 'Итог (план)',
    data: data.map((row) => Number(row[1])),
    color: '#4768BC',
    yAxis: 1,
    type: 'line',
    marker: {
      enabled: false,
    },
    zIndex: 5,
    visible: true,
  },
  {
    name: 'Итог (макс)',
    data: data.map((row) => Number(row[2])),
    color: '#D92424',
    yAxis: 1,
    type: 'line',
    marker: {
      enabled: false,
    },
    visible: true,
  },
]

export interface PlannedValueChartProps {
  handsontable: Handsontable
  isLoadingDown: boolean
  columnUp: Handsontable.ColumnSettings[]
  signalUpdateChart: boolean
  setSignalUpdateChart: Dispatch<SetStateAction<boolean>>
}

type ChartVariant = 'gesChart' | 'line'

export const PlannedValuesChart: FC<PlannedValueChartProps> = observer((props) => {
  const { handsontable, isLoadingDown, columnUp, signalUpdateChart, setSignalUpdateChart } = props
  const [typeDown, setTypeDown] = useState<ChartVariant>('line')
  const [options, setOptions] = useState<ChartProps['options'] | null>(null)
  const [options2, setOptions2] = useState<ChartProps['options'] | null>(null)
  const { calculationsPageStore } = useStore()
  const { dataForStation } = calculationsPageStore

  useEffect(() => {
    if (dataForStation.rows.length > 0 && signalUpdateChart) {
      const rgus = dataForStation.rgus
      const plantType = dataForStation.plantType
      const maxConsumptionHour = dataForStation.maxConsumptionHour ?? -2
      const minConsumptionHour = dataForStation.minConsumptionHour ?? -2
      const rows: CellValue[] = handsontable?.getData()?.slice(0, 24) ?? []
      setChartData(plantType, rows, rgus, maxConsumptionHour, minConsumptionHour)
      setSignalUpdateChart(false)
    }
  }, [signalUpdateChart, dataForStation]) //typeDown,dataUp,handsontable

  const setChartData = (
    plantType: PlantType | null,
    data: CellValue[],
    plantRgus: IRguCalculation[],
    maxConsumptionHour: number,
    minConsumptionHour: number,
  ) => {
    const chart1: SeriesOptionsType[] = [
      {
        name: 'ГЭС',
        data: data.map((row) => Number(Number(row[1]).toFixed(3))),
        color: '#4169E1',
        borderColor: 'none',
        type: 'column',
        yAxis: 1,
        visible: true,
        borderWidth: 3,
        pointPadding: 0,
        groupPadding: 0,
      },
      {
        name: 'ТЭС',
        data: data
          .map((row) => {
            const P_GEN = Number(row[1])
            const CONSUMPT = Number(row[data[0].length - 1])

            return Number(Number(CONSUMPT - P_GEN).toFixed(3))
          })
          .filter((el) => el),
        color: '#9370DB',
        borderColor: 'none',
        type: 'column',
        yAxis: 1,
        visible: true,
        borderWidth: 3,
        pointPadding: 0,
        groupPadding: 0,
      },
    ]
    const chart2: SeriesOptionsType[] =
      plantType === 'GES' ? getGesSeriesOptions(data, plantRgus) : getDefaultSeriesOptions(data)
    const series: SeriesOptionsType[] = typeDown === 'gesChart' ? chart1 : chart2
    const dataConsumpt = data
      .map((row) => {
        const P_GEN = Number(row[1])
        const CONSUMPT = Number(row[data[0].length - 1])

        return Number(Number(CONSUMPT - P_GEN).toFixed(3))
      })
      .filter((el) => el)

    const dataConsumpt2 = data.map((row) => Number(row[data[0].length - 1]))
    const minConsumpt =
      typeDown === 'gesChart' ? Math.min.apply(null, dataConsumpt) * 0.95 : Math.min.apply(null, dataConsumpt2) * 0.95

    const options: Highcharts.Options = {
      chart: {
        height: CHART_HEIGHT,
        scrollablePlotArea: {
          scrollPositionX: 400,
          scrollPositionY: 400,
          minWidth: 700,
          minHeight: 700,
        },
        // TODO: установка высоты графика
        // events: {
        //   fullscreenOpen: function () {
        //     // Получаем высоту экрана
        //     const screenHeight = window.screen.height;
        //     // Устанавливаем высоту графика равной высоте экрана
        //     this.chartHeight = screenHeight;
        //     this.redraw(); // Перерисовываем график
        //   },
        //   fullscreenClose: function () {
        //     // Устанавливаем высоту графика обратно в 310
        //     this.chartHeight = CHART_HEIGHT;
        //     this.redraw(); // Перерисовываем график
        //   }
        // }
      },
      title: {
        text: '',
      },
      legend: {
        className: typeDown === 'gesChart' ? '' : 'legend-with-border',
      },
      xAxis: [
        {
          categories: hours,
          crosshair: true,
          title: {
            text: 'Час',
          },
          plotBands: [],
          zIndex: 2,
        },
      ],
      yAxis: [
        {
          min: minConsumpt,
          tickPixelInterval: 25,
          zIndex: 2,
          labels: {
            format: '{value}',
            style: {
              color: '#ffae00',
            },
          },
          title: {
            text: typeDown === 'gesChart' ? '' : 'МВт',
            style: {
              color: '#ffae00',
            },
          },
          opposite: typeDown !== 'gesChart',
        },
        {
          gridLineWidth: 1,
          zIndex: 3,
          title: {
            text: 'МВт',
          },
          labels: {
            format: '{value}',
          },
          tickPixelInterval: 25,
        },
      ],
      tooltip: {
        shared: true,
        // TODO: раскомментировать, когда попросит аналитик
        // positioner: function() { //boxWidth, boxHeight, point
        //   const chart = this.chart; // get plotTop;
        //   console.log({ chart })
        //   return {
        //     x: 0, //chart.plotLeft + chart.plotSizeX - 100,
        //     y: chart.plotTop + chart.plotSizeY + chart.legend.legendHeight + 54 //+ 20
        //   };
        // },
        useHTML: true,
        headerFormat: '<table class="table-chart-tooltip"><tr><th><b>{point.key}</b></th></tr>',
        pointFormat:
          '<tr><td style="color: {series.color};fill:red">●</td><td>{series.name} ' +
          '</td>' +
          '<td style="text-align: right"><b>{point.y}</b></td></tr>',
        footerFormat: '</table>',
        outside: true,
        shadow: false,
        borderWidth: 0,
        split: false,
        enabled: false,
        backgroundColor: 'rgba(255,255,255,0.8)',
      },
      series,
      responsive: {
        rules: [
          {
            condition: {
              maxWidth: 500,
            },
            chartOptions: {
              yAxis: [
                {
                  labels: {
                    align: 'right',
                    x: 0,
                    y: -6,
                  },
                  showLastLabel: false,
                },
                {
                  labels: {
                    align: 'left',
                    x: 0,
                    y: -6,
                  },
                  showLastLabel: false,
                },
                {
                  visible: false,
                },
              ],
            },
          },
        ],
      },
      plotOptions: {
        column: {
          stacking: 'normal',
        },
        series: {
          borderWidth: 3,
          lineWidth: 3,
          events: {
            legendItemClick: function (e) {
              let yAxis1
              if (typeDown !== 'line') {
                const minConsumpt = Math.min.apply(null, dataConsumpt) * 0.95
                yAxis1 = this.chart.yAxis[0]
                const yAxis2 = this.chart.yAxis[1]
                const seriesName = e.target.userOptions.name
                const visible = e.target.userOptions.visible
                if (seriesName === 'ТЭС') {
                  yAxis1.update({ min: 0 })
                  yAxis2.update({ min: 0 })
                }
                if (seriesName === 'ТЭС' && visible) {
                  yAxis1.update({ min: 0 })
                  yAxis2.update({ min: 0 })
                }
                if (seriesName === 'ТЭС' && !visible) {
                  yAxis1.update({ min: minConsumpt })
                  yAxis2.update({ min: minConsumpt })
                }
              } else {
                const minConsumpt = Math.min.apply(null, dataConsumpt2) * 0.95
                yAxis1 = this.chart.yAxis[0]
                yAxis1.update({ min: minConsumpt })
              }
            },
          },
        },
      },
      exporting: {
        buttons: {
          contextButton: {
            menuItems: ['viewFullscreen', 'downloadPNG', 'downloadSVG'],
          },
        },
      },
    }

    const consumptionData = series.find((item) => item.name === 'Потребление')?.data ?? []

    if (consumptionData !== null && consumptionData.length) {
      let minValue = {
        value: consumptionData[0]!,
        index: 0,
      }
      let maxValue = {
        value: consumptionData[0]!,
        index: 0,
      }

      for (let index = 1; index < consumptionData.length; index++) {
        if (consumptionData[index]! < minValue.value)
          minValue = {
            value: consumptionData[index]!,
            index,
          }

        if (consumptionData[index]! > maxValue.value)
          maxValue = {
            value: consumptionData[index]!,
            index,
          }
      }

      const calcIndexesMin = (): { from: number; to: number } => {
        const min = minConsumptionHour - 2.5
        const max = minConsumptionHour + 0.5 //+ 1.5

        return {
          from: min <= -0.5 ? -0.5 : min,
          to: max >= 23.5 ? 23.5 : max,
        }
      }
      const calcIndexesMax = (): { from: number; to: number } => {
        const min = maxConsumptionHour - 2.5
        const max = maxConsumptionHour + 0.5

        return {
          from: min <= -0.5 ? -0.5 : min,
          to: max >= 23.5 ? 23.5 : max,
        }
      }

      if (minConsumptionHour !== -2) {
        ;(options.xAxis as XAxisOptions[])[0].plotBands!.push({
          color: '#00800020',
          zIndex: 20,
          ...calcIndexesMin(),
        })
      }
      if (maxConsumptionHour !== -2) {
        ;(options.xAxis as XAxisOptions[])[0].plotBands!.push({
          color: '#ff000020',
          zIndex: 20,
          ...calcIndexesMax(),
        })
      }
    }
    const options2 = { ...options }
    setOptions2(options2)
    setOptions(options)
  }

  const setToggleValue = (value: ChartVariant) => {
    setTypeDown(value)
    setSignalUpdateChart(true)
  }

  const renderChart = useCallback(() => {
    if (typeDown === 'gesChart' && options) {
      return (
        <Chart
          options={{
            ...options,
            series: columnUp.length === 1 ? [] : options.series,
          }}
          isLoading={isLoadingDown}
        />
      )
    }
    if (options2) {
      return (
        <Chart
          options={{
            ...options2,
            series: columnUp.length === 1 ? [] : options2.series,
          }}
          isLoading={isLoadingDown}
        />
      )
    }
  }, [options, options2, columnUp, isLoadingDown])

  return (
    <div className={cls.container}>
      {columnUp.length > 1 && (
        <Toggle<ChartVariant>
          items={[
            {
              value: 'gesChart',
              label: <Icon width={16} name='gesChart' />,
            },
            {
              value: 'line',
              label: <Icon width={16} name='chartLine' />,
            },
          ]}
          value={typeDown}
          setValue={setToggleValue}
        />
      )}
      <div className={cls.chartContainer}>{renderChart()}</div>
    </div>
  )
})
