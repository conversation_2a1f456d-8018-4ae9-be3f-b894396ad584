import { IntegratedFiltering } from '@devexpress/dx-react-grid'
import CheckRoundedIcon from '@mui/icons-material/CheckRounded'
import CloseRoundedIcon from '@mui/icons-material/CloseRounded'
import PriorityHighIcon from '@mui/icons-material/PriorityHigh'
import SyncRoundedIcon from '@mui/icons-material/SyncRounded'
import VisibilityIcon from '@mui/icons-material/Visibility'
import { Checkbox, FormControlLabel } from '@mui/material'
import { format } from 'date-fns'
import { TIME_LOADER } from 'entities/constants.ts'
import { observer } from 'mobx-react'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import {
  type IRegistryProtocolParams,
  type IRegistryProtocolResponse,
  type taskTypes,
} from 'shared/api/nsiManager/nsiManager'
import { classNames } from 'shared/lib/classNames/classNames'
import { But<PERSON> } from 'shared/ui/Button'
import { DateRangePicker } from 'shared/ui/DateRangePicker'
import { DataPickerValue, DatePickerContext } from 'shared/ui/DateRangePicker/ui/DateRangePicker'
import { Icon } from 'shared/ui/Icon'
import { Modal } from 'shared/ui/Modal'
import { useStore } from 'stores/useStore'
import { CustomTableCell, Table } from 'widgets/Table'

import { ModalActionLogProtocol } from '../../ModalActionLogProtocol'
import cls from './ModalUploadStations.module.scss'

interface ModalActionLogProps {
  className?: string
  onClose?: () => void
}

export interface IRow {
  code: string
  title: string
  tabId: string
}

const getIconStatus = (status: string) => {
  switch (status) {
    case 'IN_PROCESS':
      return <SyncRoundedIcon className={cls.ProcessIcon} color='primary' width={20} height={20} />
    case 'DONE':
      return <CheckRoundedIcon className={cls.SuccessIcon} color='success' width={20} height={20} />
    case 'DONE_WITH_ERRORS':
      return <CloseRoundedIcon className={cls.ErrorIcon} color='error' width={20} height={20} />
    case 'DONE_WITH_WARNINGS':
      return <PriorityHighIcon color='warning' width={20} height={20} />
    default:
      return <PriorityHighIcon color='warning' width={20} height={20} />
  }
}

export const getColorType = (code: string) => {
  switch (code) {
    case 'SYNC_REGISTRY':
      return '#A759E4'
    case 'COMPARE_PLANTS':
    case 'APPLY_PLANTS':
      return '#28178E'
    case 'EDIT_REGISTRY':
      return '#4F7ED9'
    default:
      return 'red'
  }
}

const TYPES_FILTER: Array<{
  name: taskTypes
  label: string | JSX.Element
  checked: boolean
}> = [
  {
    name: 'SYNC_REGISTRY',
    label: (
      <div className={cls.TypeTaskSmall} style={{ backgroundColor: getColorType('SYNC_REGISTRY') }}>
        Обновление НСИ
      </div>
    ),
    checked: false,
  },
  {
    name: 'COMPARE_PLANTS',
    label: (
      <div className={cls.TypeTaskSmall} style={{ backgroundColor: getColorType('COMPARE_PLANTS') }}>
        Загрузка станций (получение)
      </div>
    ),
    checked: false,
  },
  {
    name: 'APPLY_PLANTS',
    label: (
      <div className={cls.TypeTaskSmall} style={{ backgroundColor: getColorType('APPLY_PLANTS') }}>
        Загрузка станций (сохранение)
      </div>
    ),
    checked: false,
  },
  {
    name: 'EDIT_REGISTRY',
    label: (
      <div className={cls.TypeTaskSmall} style={{ backgroundColor: getColorType('EDIT_REGISTRY') }}>
        Редактирование НСИ
      </div>
    ),
    checked: false,
  },
]

export interface IProtocolData extends IRegistryProtocolResponse {
  status: string
  userFio: string
  type: {
    title: string
    code: string
  }
}

export interface IParams extends Omit<Omit<IRegistryProtocolParams, 'fromDate'>, 'toDate'> {
  fromDate: Date | null
  toDate: Date | null
}

export const ModalActionLog = observer((props: ModalActionLogProps) => {
  const { className, onClose } = props
  const { nsiStore } = useStore()
  const { registryProtocol, getRegistryProtocol } = nsiStore
  const [params, setParams] = useState<IParams>(() => ({
    fromDate: new Date(),
    toDate: new Date(),
  }))
  const [isVisibleProtocol, setIsVisibleProtocol] = useState<boolean>(false)
  const [protocolData, setProtocolData] = useState<IProtocolData | null>(null)
  const [filter, setFilter] = useState(TYPES_FILTER)
  const [loading, setLoading] = useState(true)
  const tableContainerRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    setLoading(true)
    getRegistryProtocol(params as IRegistryProtocolParams).then(() => {
      setTimeout(() => {
        setLoading(false)
      }, TIME_LOADER)
    })
  }, [params])

  const registryList = useMemo(() => {
    if (!registryProtocol.length) return []

    return registryProtocol.map((item) => ({ tabId: item.uuid, ...item }))
  }, [registryProtocol])

  const handleChangeDate = (value: DataPickerValue, context: DatePickerContext) => {
    if (context.validationError.some((item) => item)) return

    const [from, to] = value
    setParams((prev) => ({
      ...prev,
      fromDate: from,
      toDate: to,
    }))
  }

  const columns = [
    {
      name: 'updatedDate',
      title: 'Дата выполнения',
      width: 200,
      render: (value: string) => format(new Date(value), 'dd.MM.yyy HH:mm:ss'),
    },
    {
      name: 'type',
      title: 'Действие',
      width: 300,
      render: (type: IRow) => {
        return (
          <CustomTableCell className={cls.Td}>
            <div className={cls.TypeTask} style={{ backgroundColor: getColorType(type.code) }}>
              {type.title}
            </div>
          </CustomTableCell>
        )
      },
      customSorting: (a: IRow, b: IRow) => {
        return a.title < b.title ? -1 : 1
      },
      customSearching: (value: IRow, filter: { value: string; columnName: string }, row: IRow) => {
        if (value.title.toLowerCase().includes(filter.value.toLowerCase())) return true

        return IntegratedFiltering.defaultPredicate(value, filter, row)
      },
    },
    {
      name: 'userFio',
      title: 'Инициатор',
      width: 250,
      render: (value: string) => {
        return <>{value.trim().toUpperCase() === 'SYSTEM' ? <Icon name='ghost' width={18} /> : <>{value}</>}</>
      },
    },
    {
      name: 'status',
      title: 'Статус',
      headRender: () => {
        return <div className={cls.ActionHeader}>Статус</div>
      },
      width: 80,
      render: (status: string) => {
        return <div className={cls.IconCell}>{getIconStatus(status)}</div>
      },
    },
    {
      name: 'actions',
      title: 'Протокол',
      width: 80,
      headRender: () => {
        return <div className={cls.ActionHeader}>Протокол</div>
      },
      render: (_: unknown, row: IRow) => {
        const handleClickToMenuItem = () => {
          const find = registryProtocol.find((item) => item.uuid === row.tabId)
          setProtocolData(find ? (find as IProtocolData) : null)
          setIsVisibleProtocol(true)
        }

        return (
          <div className={cls.IconCell}>
            <Button className={cls.VisibilityButton} variant='text' onClick={handleClickToMenuItem}>
              <VisibilityIcon />
            </Button>
          </div>
        )
      },
    },
  ]

  const handleChangeFilter = (values: taskTypes[]) => {
    const newFilter = [...filter].map((item) => ({
      ...item,
      checked: values.includes(item.name),
    }))
    setFilter(newFilter)
    setParams((prev) => ({
      ...prev,
      taskTypes: values,
    }))
  }

  const handleChangeFilters = (_: unknown, event: React.ChangeEvent<HTMLInputElement>) => {
    const newFilter = [...filter]
    const index = newFilter.findIndex((item) => item.name === event.target.name)
    newFilter.splice(index, 1, {
      ...newFilter[index],
      checked: event.target.checked,
    })
    handleChangeFilter(newFilter.filter((item) => item.checked).map((item) => item.name))
    setFilter(newFilter)
  }

  return (
    <Modal
      fullScreen={false}
      open
      fullWidth
      maxWidth='lg'
      title='История действий'
      className={classNames(cls.ModalActionLog, {}, className ? [className] : [])}
      onClose={onClose}
    >
      <div className={cls.DateContainer}>
        <DateRangePicker dateFrom={params.fromDate} dateTo={params.toDate} handleChangeDate={handleChangeDate} />
      </div>
      <div ref={tableContainerRef} className={classNames(cls.TableContainer, {}, [])}>
        <Table
          rows={registryList}
          columns={columns}
          loading={loading}
          height={tableContainerRef.current?.clientHeight}
          columnSearchDisabled={['actions', 'status']}
          headerComponents={
            <>
              <div className={cls.TopFilters}>
                <h4 className={cls.TopFilterTitle}>Действие</h4>
                {filter.map((item) => (
                  <div className={cls.FilterItem}>
                    <FormControlLabel
                      key={item.name}
                      control={
                        <Checkbox
                          className={cls.TopFiltersCheckbox}
                          checked={item.checked}
                          name={item.name}
                          onChange={(e) => handleChangeFilters('taskTypes', e)}
                        />
                      }
                      label={item.label}
                    />
                  </div>
                ))}
              </div>
            </>
          }
        />
      </div>
      {isVisibleProtocol && protocolData && (
        <ModalActionLogProtocol
          onClose={() => {
            setIsVisibleProtocol(false)
          }}
          data={protocolData}
          resetData={() => {
            setProtocolData(null)
          }}
        />
      )}
    </Modal>
  )
})
