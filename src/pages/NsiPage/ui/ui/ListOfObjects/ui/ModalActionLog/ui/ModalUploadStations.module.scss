.TableContainer {
  height: calc(100% - 30px);
}

.DateContainer {
  width: 220px;
  margin-top: -8px;
}

.ModalActionLog > div > div {
  width: 950px;
  height: 900px;
}

.IconCell {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.TypeTask {
  width: fit-content;
  padding: 0.01em 0.6em;
  border-radius: 4px;
  color: #fff;
}

.TypeTaskSmall {
  width: fit-content;
  padding: 0.01em 0.3em;
  border-radius: 4px;
  color: #fff;
}

.Td {
  display: flex;
  align-items: center;
}

.VisibilityButton {
  padding: 0 !important;
  margin: 0 !important;
}

.ActionHeader {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.RowFilter {
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.TopFilters {
  display: flex;
  align-items: center;
  gap: 0.1em;
}

.TopFilterTitle {
  margin-right: 1em;
}

.FilterItem {
  div {
    padding: 0.01em 0.6em !important;
    border-radius: 4px;
  }
  span:last-child {
    color: var(--primary-color-invert);
  }
}

.TopFiltersCheckbox {
  padding: 1px !important;
}

.ErrorIcon {
  stroke: var(--red-color);
  stroke-width: 1px;
}

.SuccessIcon {
  stroke: var(--green-color);
  stroke-width: 1px;
}

.ProcessIcon {
  stroke: var(--primary-color);
  stroke-width: 1px;
}
