import { VARIABLES_OFFSET_DAYS, VARIABLES_UNLOADING_NEXT_DAYS } from '../config/const'
import { Values } from '../config/types'
import { getRequiredFields } from '.'

export const validation = (reportType: string, values: Values) => {
  const errors: Record<string, string> = {}

  const requiredFields = getRequiredFields(reportType)

  for (const [key, { newValue }] of Object.entries(values)) {
    if (VARIABLES_OFFSET_DAYS.includes(key)) {
      if (values.offsetDaysTurnedOn.newValue && newValue === null) {
        errors[key] = 'Поле должно быть заполнено'
        errors.offsetDaysTurnedOn = 'Все поля должны быть заполнены значениями от -7 до 5'
      }
    } else if (VARIABLES_UNLOADING_NEXT_DAYS.includes(key)) {
      if (values.unloadingNextDaysTurnedOn.newValue && newValue === null) {
        errors[key] = 'Поле должно быть заполнено'
        errors.unloadingNextDaysTurnedOn = 'Все поля должны быть заполнены значениями от 0 до 14'
      }
    } else if (
      reportType === 'PLAN_GENERATION' &&
      values.tableTitle.newValue &&
      values.unloadingFormat.newValue === 'XLSX' &&
      key === 'tableTitleTemplate' &&
      !newValue
    ) {
      errors[key] = 'Поле должно быть заполнено'
    } else if (
      reportType === 'PLAN_GENERATION' &&
      values.tableTitle.newValue &&
      key === 'tableTitleRow' &&
      newValue === 0
    ) {
      errors[key] = 'Значение должно быть больше нуля'
    } else if (
      reportType === 'PLAN_GENERATION' &&
      values.tableHeader.newValue &&
      key === 'tableHeaderRow' &&
      newValue === 0
    ) {
      errors[key] = 'Значение должно быть больше нуля'
    } else if (key === 'firstHourRow' && newValue === 0) {
      errors[key] = 'Значение должно быть больше нуля'
    } else if (key === 'firstHourColumn' && newValue === 0) {
      errors[key] = 'Значение должно быть больше нуля'
    } else if (key === 'dateRow' && values.putDate.newValue && newValue === 0) {
      errors[key] = 'Значение должно быть больше нуля'
    } else if (key === 'dateColumn' && values.putDate.newValue && newValue === 0) {
      errors[key] = 'Значение должно быть больше нуля'
    } else if (
      values.putDate.newValue &&
      (key === 'dateRow' || key === 'dateColumn') &&
      (newValue === null || newValue === '')
    ) {
      errors[key] = 'Поле должно быть заполнено'
    } else if (
      values.sendFromCalculationPage.newValue &&
      key === 'calculationPagePlants' &&
      Array.isArray(newValue) &&
      newValue.length === 0
    ) {
      errors[key] = 'Поле должно быть заполнено'
    } else if (!values.summaryFormat.newValue && key === 'plants' && Array.isArray(newValue) && newValue.length === 0) {
      errors[key] = 'Поле должно быть заполнено'
    } else if (requiredFields.has(key) && typeof newValue !== 'number' && !(newValue as string | [])?.length) {
      errors[key] = 'Поле должно быть заполнено'
    }
  }

  return [!Object.keys(errors).length, errors]
}
