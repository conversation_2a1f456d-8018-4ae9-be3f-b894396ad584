import { addDays, format, isAfter, isEqual, parse } from 'date-fns'

import { DATE_FORMATS } from '../../../entities/constants'
import { locationParse } from '../locationParse'

export const formatDateTime = (dateTime: string | Date) => {
  let formattedDateTime: Date | null = null

  if (typeof dateTime === 'string' && (dateTime.includes('0000-01-01T') || dateTime.includes('T0000-01-01'))) {
    throw new Error('Invalid time value')
  }

  if (typeof dateTime === 'string' && dateTime.includes('T')) {
    formattedDateTime = new Date(dateTime.trim())
  } else {
    formattedDateTime = typeof dateTime === 'string' ? new Date(dateTime.trim()) : dateTime
  }

  return format(formattedDateTime, 'dd.MM.yyyy HH:mm:ss')
}

export const formatDate = (date: string | Date) => {
  return format(new Date(date), 'dd.MM.yyyy')
}

export const formatDaysToReadableStr = (numDays: number): string => {
  if (numDays < 0) {
    return 'Отрицательное количество дней'
  }

  const lastDigit = numDays % 10
  const lastTwoDigits = numDays % 100

  if (lastTwoDigits >= 11 && lastTwoDigits <= 19) {
    return `${numDays} дней`
  }

  switch (lastDigit) {
    case 1:
      return `${numDays} день`
    case 2:
    case 3:
    case 4:
      return `${numDays} дня`
    default:
      return `${numDays} дней`
  }
}

export const parseISO8601DateMonth = (date: string): Date => {
  return parse(date, '--MM-dd', new Date())
}

export const readableISO8601DateMonth = (date: string): string => {
  return format(parse(date, '--MM-dd', new Date()), DATE_FORMATS.ddMM)
}

/**
 * Преобразует дату из формата "DD.MM.YYYY" в формат "YYYY-MM-DD".
 *
 * @param {string|null|unknown} date - Дата в формате "DD.MM.YYYY" или null|unknown.
 * @returns {string|null} Дата в формате "YYYY-MM-DD" или null, если входная дата не является строкой или имеет неправильный формат.
 */
export const convertDate = (date: string | null | unknown): string | null => {
  if (typeof date !== 'string') return null

  const [day, month, year] = date.split('.')
  if (!day || !month || !year) return null

  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
}

export const getDateByUrlOrNextDateAfterCurrentByCurrentRoute = (route: string) => {
  const { year = null, month = null, day = null } = locationParse(location.search)
  if (year && month && day && window.location.pathname.includes(route)) {
    return new Date(`${year}-${month}-${day}`)
  }

  return addDays(new Date(), 1)
}

export const isSameOrAfter = (date: Date, dateToCompare: Date) =>
  isEqual(date, dateToCompare) || isAfter(date, dateToCompare)
