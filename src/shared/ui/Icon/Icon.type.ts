import { CSSProperties } from 'react'

export interface IconProps {
  name: IconNameProps
  width?: number | string
  height?: number | string
  title?: string
  onClick?: () => void
  className?: string
  styleIcon?: CSSProperties
}

export type IconNameProps =
  | 'whiteLogo'
  | 'archiveMinus'
  | 'archivePlus'
  | 'noData'
  | 'arrow'
  | 'arrowLeft'
  | 'arrowTop'
  | 'arrowBottom'
  | 'nsi'
  | 'openLock'
  | 'calcModel'
  | 'calc'
  | 'administration'
  | 'avatarDefault'
  | 'arrowSmall'
  | 'logout'
  | 'upload'
  | 'update'
  | 'backTime'
  | 'journal'
  | 'calendarAdd'
  | 'calendarEdit'
  | 'calendarDone'
  | 'calendarDelete'
  | 'groups'
  | 'block'
  | 'exclamation'
  | 'sync'
  | 'checkCircle'
  | 'exclamationTriangle'
  | 'timesCircle'
  | 'watch'
  | 'user'
  | 'networkWired'
  | 'circlePlus'
  | 'circleMinus'
  | 'circleInfo'
  | 'closeLock'
  | 'rge'
  | 'department'
  | 'plant'
  | 'generator'
  | 'sortCustom'
  | 'sortAlphabeat'
  | 'search'
  | 'settings'
  | 'star'
  | 'view'
  | 'trash'
  | 'information'
  | 'plus'
  | 'points'
  | 'book'
  | 'ghost'
  | 'dragAndDrop'
  | 'time'
  | 'loadStation'
  | 'history'
  | 'leaf'
  | 'lightning'
  | 'chartBar'
  | 'chartLine'
  | 'noView'
  | 'calculationOfPermissibleZones'
  | 'calculationOfThePlannedGenerationSchedule'
  | 'enteringIntoAnAcceptableAreaRelativeToTheConsumptionSchedule'
  | 'enteringIntoAnAcceptableAreaRelativeToTheNearestBorder'
  | 'loadData'
  | 'loadTelemetry'
  | 'isp'
  | 'modes'
  | 'rm'
  | 'gesChart'
  | 'exit'
  | 'filter'
  | 'reports'
  | 'unloading'
  | 'newsletter'
  | 'calcRGE'
  | 'warning'
  | 'error'
  | 'journals'
  | 'excel'
  | 'modesWrite'
  | 'arrowForDatePicker'
  | 'doubleArrowForDatePicker'
  | 'backArrowForDatePicker'
  | 'backDoubleArrowForDatePicker'
  | 'wave'
  | 'bulb'
  | 'model'
  | 'accumulator'
  | 'max'
  | 'clear'
  | 'close'
  | 'bigArrowLeft'
  | 'snowflake'
  | 'sun'
